import React from 'react';
import { getStatementOfOperationsByThousand } from '@/api/statementOfOperationsApi';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { Container } from '@/components/common/container';
import PropertyPackageReportFilters from '@/pages/report/components/PropertyPackageReportFilters';
import { createStatementOfOperationsColumns } from '../components/ReportTable';
import StatementErrorState from '../components/StatementErrorState';
import StatementLoadingState from '../components/StatementLoadingState';
import StatementTable from '../components/StatementTable';
import { useStatementDataWithState } from '../hooks/useStatementData';
import { formatThousandStatementData } from '../utils/dataTransformers';
import {
  exportStatementToExcel,
  exportStatementToPDF,
} from '../utils/formatters';
import {
  convertThousandTotalForReportTable,
  formatThousandCellValue,
} from '../utils/formatting';
import { formatPeriodDisplay, sortCategories } from '../utils/sorting';

const StatementOfOperationsByThousand: React.FC = () => {
  const { lastClosedMonth, loading, error, expandedCategories, filters } =
    useSelector((state: RootState) => state.incomeReport);

  const statementData = useStatementDataWithState(
    getStatementOfOperationsByThousand,
    formatThousandStatementData,
  );

  const handleExportToExcel = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(statementData.categoryData);
    exportStatementToExcel(
      sortedCategoryData,
      lastClosedMonth,
      expandedCategories,
      filters,
    );
  };

  const handleExportToPDF = () => {
    if (!statementData) return;
    const sortedCategoryData = sortCategories(statementData.categoryData);
    exportStatementToPDF(
      sortedCategoryData,
      lastClosedMonth,
      expandedCategories,
      filters,
    );
  };

  const formatCellValue = (
    value: string | number,
    columnId: string,
    category: { title?: string; total?: any },
  ) => {
    const isMarginCategory = category?.title?.includes('MARGIN');
    const isUnitsCategory = category?.title === 'Units';
    return formatThousandCellValue(
      value,
      columnId,
      isMarginCategory,
      isUnitsCategory,
      category?.total,
    );
  };

  if (loading) {
    return <StatementLoadingState />;
  }

  if (error) {
    return <StatementErrorState error={error} />;
  }

  const sortedCategoryData = statementData
    ? sortCategories(statementData.categoryData)
    : [];
  const statementColumns = createStatementOfOperationsColumns();

  return (
    <Container title="Statement of Operations (in 000's)" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="flex items-end gap-3 mb-6">
          <PropertyPackageReportFilters
            businessFilter={false}
            handleExportToExcel={handleExportToExcel}
            handleExportToPDF={handleExportToPDF}
          />
        </div>

        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg">
          <div className="min-w-[1024px]">
            <div className="bg-[#F4F4FF] p-4 mb-4 rounded-t-lg flex justify-between items-center">
              <h3 className="text-lg font-semibold mb-2 text-[#43298F]">
                Period : {formatPeriodDisplay(filters.month, filters.year)}
              </h3>
            </div>

            <div className="p-4">
              <StatementTable
                categories={sortedCategoryData}
                columns={statementColumns}
                expandedCategories={expandedCategories}
                leftGroupTitle="Year to Date"
                rightGroupTitle="Full Year"
                year={filters?.year || '2025'}
                convertTotalForReportTable={convertThousandTotalForReportTable}
                formatCellValue={formatCellValue}
              />
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default StatementOfOperationsByThousand;
