import React, { useState } from 'react';
import { getLeaseLineItems } from '@/api/leaseLineItemsApi';
import { RentalMetric } from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { LeaseLineItemRequest } from '@/types/leaseLineItemsTypes';
import { RESPONSIVE_FONTS } from '../constants/typography';
import { getTextColorClass } from '../utils/colorUtils';
import { exportLeaseLineItemsToExcel } from '../utils/leaseLineItemsExport';
import LoadingSkeleton from './shared/LoadingSkeleton';

const RentalMetrics: React.FC = () => {
  const { rentalMetrics, loadingStates, filters, propertyInfo } = useSelector(
    (state: RootState) => state.scorecard,
  );
  const isLoading = loadingStates.rentalMetrics;
  const [loadingCardIndex, setLoadingCardIndex] = useState<number | null>(null);
  const [isExporting, setIsExporting] = useState<boolean>(false);

  const handleMetricClick = async (metric: RentalMetric, index: number) => {
    if (isExporting) {
      toast.warning('Please wait for the current export to complete');
      return;
    }

    const clickableMetrics = [
      {
        label: 'NEW NET IN PLACE RENT',
        rnkFilter: '1' as const,
        rentType: 'new' as const,
      },
      {
        label: 'RENEWAL NET IN PLACE RENT',
        rnkFilter: '>1' as const,
        rentType: 'renewal' as const,
      },
      {
        label: 'NET IN PLACE RENT',
        rnkFilter: '>=1' as const,
        rentType: 'all' as const,
      },
    ];

    const clickableMetric = clickableMetrics.find(
      (cm) => cm.label === metric.label,
    );

    if (!clickableMetric) return;

    const propertyCode = filters.property
      ? filters.property.split(' - ')[0]
      : propertyInfo.propertyCode !== '0'
        ? propertyInfo.propertyCode
        : null;

    if (!propertyCode || !filters.startDate || !filters.endDate) {
      toast.error('Please select property and date range first');
      return;
    }

    setIsExporting(true);
    setLoadingCardIndex(index);

    try {
      const request: LeaseLineItemRequest = {
        startDate: filters.startDate,
        endDate: filters.endDate,
        propertyCode: propertyCode,
        rnkFilter: clickableMetric.rnkFilter,
      };

      const response = await getLeaseLineItems(request);

      if (response.data && response.data.length > 0) {
        await exportLeaseLineItemsToExcel(
          response.data,
          propertyCode,
          filters.startDate,
          filters.endDate,
          clickableMetric.rentType,
        );
      } else {
        toast.warning('No data found for the selected criteria');
      }
    } catch (error) {
      console.error('Error fetching lease line items:', error);
      toast.error('Failed to fetch lease line items');
    } finally {
      setLoadingCardIndex(null);
      setIsExporting(false);
    }
  };

  const isClickableMetric = (label: string) => {
    return [
      'NEW NET IN PLACE RENT',
      'RENEWAL NET IN PLACE RENT',
      'NET IN PLACE RENT',
    ].includes(label);
  };

  const renderPairedCard = (
    metric1: RentalMetric,
    metric2: RentalMetric,
    key: string,
    index1: number,
    index2: number,
  ) => (
    <div key={key} className="py-1 sm:py-2 px-1 bg-white flex-1 h-full">
      <div className="flex items-stretch h-full">
        <div
          className={`flex-1 text-center flex flex-col justify-center px-1 min-w-0 ${
            isClickableMetric(metric1?.label) && !isExporting
              ? 'cursor-pointer hover:bg-gray-50 transition-colors rounded'
              : isClickableMetric(metric1?.label) && isExporting
              ? 'cursor-not-allowed'
              : ''
          } ${loadingCardIndex === index1 ? 'opacity-50' : ''}`}
          onClick={() => !isExporting && handleMetricClick(metric1, index1)}
        >
          <div className="flex flex-col items-center">
            <div
              className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric1?.color)}`}
            >
              {loadingCardIndex === index1 ? 'Loading...' : metric1?.value}
            </div>
            <div
              className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}
            >
              {metric1?.label}
            </div>
          </div>
        </div>
        <div className="w-px bg-gray-300 mx-1 sm:mx-2 self-stretch"></div>
        <div
          className={`flex-1 text-center flex flex-col justify-center px-1 min-w-0 ${
            isClickableMetric(metric2?.label) && !isExporting
              ? 'cursor-pointer hover:bg-gray-50 transition-colors rounded'
              : isClickableMetric(metric2?.label) && isExporting
              ? 'cursor-not-allowed'
              : ''
          } ${loadingCardIndex === index2 ? 'opacity-50' : ''}`}
          onClick={() => !isExporting && handleMetricClick(metric2, index2)}
        >
          <div className="flex flex-col items-center">
            <div
              className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric2?.color)}`}
            >
              {loadingCardIndex === index2 ? 'Loading...' : metric2?.value}
            </div>
            <div
              className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}
            >
              {metric2?.label}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="h-full flex flex-col">
        <div
          className="p-1 sm:p-2 flex-shrink-0"
          style={{ backgroundColor: '#5C6BC0' }}
        >
          <div className="h-4 bg-gray-300 rounded w-64 animate-pulse"></div>
        </div>
        <div className="mt-1 sm:mt-2 flex-1">
          <LoadingSkeleton type="metrics-grid" columns={4} className="h-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div
        className="p-1 sm:p-2 flex-shrink-0"
        style={{ backgroundColor: '#5C6BC0' }}
      >
        <h3
          className={`font-semibold text-white ${RESPONSIVE_FONTS.tableContent} tracking-wide`}
        >
          Net In Place Effective Rents, Year Over Year Change, Renewal
          Conversion
        </h3>
      </div>

      {/* Metrics */}
      <div className="flex justify-between gap-1 sm:gap-2 mt-1 sm:mt-2 flex-1">
        {/* Group all metrics in pairs */}
        {Array.from(
          { length: Math.ceil(rentalMetrics.length / 2) },
          (_, pairIndex) => {
            const startIndex = pairIndex * 2;
            const metric1 = rentalMetrics[startIndex];
            const metric2 = rentalMetrics[startIndex + 1];

            if (metric2) {
              return renderPairedCard(
                metric1,
                metric2,
                `pair-${pairIndex}`,
                startIndex,
                startIndex + 1,
              );
            } else {
              return (
                <div
                  key={`single-${pairIndex}`}
                  className={`py-1 sm:py-2 px-1 bg-white flex-1 text-center h-full flex flex-col justify-center min-w-0 ${
                    isClickableMetric(metric1?.label) && !isExporting
                      ? 'cursor-pointer hover:bg-gray-50 transition-colors rounded'
                      : isClickableMetric(metric1?.label) && isExporting
                      ? 'cursor-not-allowed'
                      : ''
                  } ${loadingCardIndex === startIndex ? 'opacity-50' : ''}`}
                  onClick={() => !isExporting && handleMetricClick(metric1, startIndex)}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`${RESPONSIVE_FONTS.metricValue} font-bold h-6 flex items-center justify-center ${getTextColorClass(metric1?.color)}`}
                    >
                      {loadingCardIndex === startIndex
                        ? 'Loading...'
                        : metric1?.value}
                    </div>
                    <div
                      className={`${RESPONSIVE_FONTS.metricLabel} text-purple-800 leading-tight font-medium mt-1 flex items-start justify-center min-h-[2rem]`}
                    >
                      {metric1?.label}
                    </div>
                  </div>
                </div>
              );
            }
          },
        )}
      </div>
    </div>
  );
};

export default RentalMetrics;
