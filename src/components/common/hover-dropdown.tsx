// Libraries
import { type MenuProps } from 'antd';
import { Button, Dropdown } from "antd";
import type { DropdownProps } from 'antd';
import {
  RiContractRightLine,
  RiFileExcel2Line,
  RiFilePdf2Line,
} from '@remixicon/react';

type HoverDropdownProps = DropdownProps & {
  size?: 'small' | 'middle' | 'large' | undefined;
  className?: string;
  onExportExcel?: () => void;
  onExportPDF?: () => void;
};

export const HoverDropdown: React.FC<HoverDropdownProps> = ({
  size = 'middle',
  className,
  onExportExcel,
  onExportPDF,
  ...props
}) => {

  const items: MenuProps['items'] = [
    onExportExcel ? {
      key: '1',
      label: (
        <div className='flex gap-2 cursor-pointer' onClick={onExportExcel}>
          <RiFileExcel2Line/>
          Export Excel
        </div>
      ),
    } : null,
    onExportPDF ? {
      key: '2',
      label: (
        <div className='flex gap-2 cursor-pointer' onClick={onExportPDF}>
          <RiFilePdf2Line/>
          Export PDF
        </div>
      ),
    } : null,
  ];

  return (
    <Dropdown {...props} menu={{ items }} className={className}>
      <Button
        size={size}
        icon={<RiContractRightLine className='mt-1 rotate-90' style={{ color: '#43298F' }} />}
        color="default"
        style={{ border: 'thin solid #43298F' }}
      />
    </Dropdown>
  );

};
